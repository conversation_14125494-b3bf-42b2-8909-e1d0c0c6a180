{"compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "moduleResolution": "node", "allowJs": false, "noEmit": false, "incremental": true, "sourceMap": true, "noImplicitAny": true, "strictNullChecks": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@shared/*": ["../shared/src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}