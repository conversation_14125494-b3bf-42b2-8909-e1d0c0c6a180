import { createTheme, ThemeOptions } from '@mui/material/styles'
import { trTR, enUS } from '@mui/material/locale'

// 🎨 RENK PALETİ - Buradan tüm renkler yönetiliyor
export const colors = {
  // Ana renkler
  primary: {
    main: '#1976d2',      // Ana mavi
    light: '#42a5f5',     // Açık mavi
    dark: '#1565c0',      // Koyu mavi
    contrastText: '#fff'
  },
  secondary: {
    main: '#dc004e',      // Ana pembe/kırmızı
    light: '#ff5983',     // Açık pembe
    dark: '#9a0036',      // Koyu kırmızı
    contrastText: '#fff'
  },

  // Durum renkleri
  success: {
    main: '#2e7d32',      // Başarı yeşili
    light: '#4caf50',     // Açık yeşil
    dark: '#1b5e20',      // Koyu yeşil
    contrastText: '#fff'
  },
  error: {
    main: '#d32f2f',      // <PERSON>a kırmızısı
    light: '#f44336',     // A<PERSON>ık kırmızı
    dark: '#c62828',      // Koyu kırmızı
    contrastText: '#fff'
  },
  warning: {
    main: '#ed6c02',      // Uyarı turuncusu
    light: '#ff9800',     // Açık turuncu
    dark: '#e65100',      // Koyu turuncu
    contrastText: '#fff'
  },
  info: {
    main: '#0288d1',      // Bilgi mavisi
    light: '#03a9f4',     // Açık mavi
    dark: '#01579b',      // Koyu mavi
    contrastText: '#fff'
  },

  // Gri tonları
  grey: {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#eeeeee',
    300: '#e0e0e0',
    400: '#bdbdbd',
    500: '#9e9e9e',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121'
  },

  // POS özel renkler
  pos: {
    cash: '#4caf50',      // Nakit yeşili
    card: '#2196f3',      // Kart mavisi
    pending: '#ff9800',   // Bekleyen turuncu
    completed: '#4caf50', // Tamamlanan yeşil
    cancelled: '#f44336', // İptal kırmızı
    kitchen: '#ff5722',   // Mutfak turuncu
    table: {
      empty: '#e8f5e8',   // Boş masa
      occupied: '#ffebee', // Dolu masa
      reserved: '#e3f2fd'  // Rezerve masa
    }
  }
}

// Türkçe tema
export const lightTheme = createTheme({
  palette: {
    mode: 'light',
    primary: colors.primary,
    secondary: colors.secondary,
    success: colors.success,
    error: colors.error,
    warning: colors.warning,
    info: colors.info,
    background: {
      default: colors.grey[50],
      paper: '#ffffff',
    },
    text: {
      primary: colors.grey[900],
      secondary: colors.grey[600],
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    // Başlıklar
    h1: {
      fontSize: '2.5rem',    // 40px
      fontWeight: 700,
      lineHeight: 1.2,
    },
    h2: {
      fontSize: '2rem',      // 32px
      fontWeight: 600,
      lineHeight: 1.3,
    },
    h3: {
      fontSize: '1.75rem',   // 28px
      fontWeight: 600,
      lineHeight: 1.3,
    },
    h4: {
      fontSize: '1.5rem',    // 24px
      fontWeight: 500,
      lineHeight: 1.4,
    },
    h5: {
      fontSize: '1.25rem',   // 20px
      fontWeight: 500,
      lineHeight: 1.4,
    },
    h6: {
      fontSize: '1rem',      // 16px
      fontWeight: 500,
      lineHeight: 1.5,
    },
    // Gövde metinleri
    body1: {
      fontSize: '1rem',      // 16px
      fontWeight: 400,
      lineHeight: 1.5,
    },
    body2: {
      fontSize: '0.875rem',  // 14px
      fontWeight: 400,
      lineHeight: 1.43,
    },
    // Buton metni
    button: {
      fontSize: '0.875rem',  // 14px
      fontWeight: 500,
      textTransform: 'none' as const,
      letterSpacing: '0.02em',
    },
    // Küçük metin
    caption: {
      fontSize: '0.75rem',   // 12px
      fontWeight: 400,
      lineHeight: 1.66,
    },
    // Overline
    overline: {
      fontSize: '0.75rem',   // 12px
      fontWeight: 400,
      textTransform: 'uppercase' as const,
      letterSpacing: '0.08em',
    },
  },
  components: {
    // 🔘 BUTONLAR
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 8,
          fontWeight: 500,
          padding: '8px 16px',
          minHeight: 40,
        },
        sizeLarge: {
          padding: '12px 24px',
          fontSize: '1rem',
          minHeight: 48,
        },
        sizeSmall: {
          padding: '6px 12px',
          fontSize: '0.8125rem',
          minHeight: 32,
        },
      },
    },

    // 📄 KARTLAR
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          border: '1px solid rgba(0,0,0,0.05)',
        },
      },
    },

    // 📝 INPUT ALANLARI
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 8,
          },
        },
      },
    },

    // 🏷️ CHIP'LER
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 6,
          fontWeight: 500,
        },
      },
    },

    // 📊 PAPER
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 8,
        },
        elevation1: {
          boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
        },
      },
    },
  },
}, trTR)

// Koyu tema
export const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#90caf9',      // Açık mavi (dark mode'da daha soft)
      light: '#e3f2fd',
      dark: '#42a5f5',
      contrastText: '#000',
    },
    secondary: {
      main: '#f48fb1',      // Açık pembe
      light: '#fce4ec',
      dark: '#e91e63',
      contrastText: '#000',
    },
    success: {
      main: '#66bb6a',      // Dark mode yeşil
      light: '#81c784',
      dark: '#4caf50',
      contrastText: '#000',
    },
    error: {
      main: '#f44336',      // Dark mode kırmızı
      light: '#e57373',
      dark: '#d32f2f',
      contrastText: '#fff',
    },
    warning: {
      main: '#ffa726',      // Dark mode turuncu
      light: '#ffb74d',
      dark: '#ff9800',
      contrastText: '#000',
    },
    info: {
      main: '#29b6f6',      // Dark mode mavi
      light: '#4fc3f7',
      dark: '#03a9f4',
      contrastText: '#000',
    },
    background: {
      default: '#121212',   // Ana arka plan
      paper: '#1e1e1e',     // Kart arka planı
    },
    text: {
      primary: '#ffffff',   // Ana metin
      secondary: '#b0b0b0', // İkincil metin
    },
  },
  typography: lightTheme.typography, // Aynı typography'yi kullan
  components: {
    ...lightTheme.components, // Aynı component stillerini kullan
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 2px 8px rgba(0,0,0,0.4)',
          border: '1px solid rgba(255,255,255,0.1)',
        },
      },
    },
  },
}, trTR)

// İngilizce temalar
export const lightThemeEN = createTheme(lightTheme, enUS)
export const darkThemeEN = createTheme(darkTheme, enUS)
