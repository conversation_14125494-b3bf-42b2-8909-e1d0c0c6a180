import React, { useState } from 'react'
import {
  Box,
  TextField,
  Button,
  Typography,
  FormControlLabel,
  Switch,
  Alert,
  CircularProgress,
  InputAdornment,
  IconButton,
} from '@mui/material'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { Visibility, VisibilityOff, Person, Lock } from '@mui/icons-material'
import { useAuthStore } from '../../store/useAuthStore'
import { LoginRequest } from '@shared/types'

interface LoginFormData extends LoginRequest {
  rememberMe: boolean
}

export const LoginForm: React.FC = () => {
  const { t } = useTranslation()
  const { login, isLoading, error, clearError } = useAuthStore()
  const [showPassword, setShowPassword] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<LoginFormData>({
    defaultValues: {
      username: '',
      password: '',
      rememberMe: false,
    },
  })

  const onSubmit = async (data: LoginFormData) => {
    clearError()
    
    try {
      await login(
        {
          username: data.username,
          password: data.password,
        },
        data.rememberMe
      )
      
      // Success - redirect will be handled by router
    } catch (error) {
      // Error is already handled in store
      console.error('Login failed:', error)
    }
  }

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword)
  }

  return (
    <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={{ width: '100%' }}>
      {/* Header */}
      <Box sx={{ mb: 4, textAlign: 'center' }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          {t('auth.welcomeBack')}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          {t('auth.loginDescription')}
        </Typography>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert 
          severity="error" 
          sx={{ mb: 3 }}
          onClose={clearError}
        >
          {error}
        </Alert>
      )}

      {/* Username Field */}
      <TextField
        {...register('username', {
          required: t('auth.enterUsername'),
          minLength: {
            value: 3,
            message: 'Kullanıcı adı en az 3 karakter olmalıdır',
          },
        })}
        fullWidth
        label={t('auth.username')}
        placeholder={t('auth.enterUsername')}
        error={!!errors.username}
        helperText={errors.username?.message}
        disabled={isLoading}
        sx={{ mb: 3 }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Person color="action" />
            </InputAdornment>
          ),
        }}
      />

      {/* Password Field */}
      <TextField
        {...register('password', {
          required: t('auth.enterPassword'),
          minLength: {
            value: 6,
            message: 'Şifre en az 6 karakter olmalıdır',
          },
        })}
        fullWidth
        type={showPassword ? 'text' : 'password'}
        label={t('auth.password')}
        placeholder={t('auth.enterPassword')}
        error={!!errors.password}
        helperText={errors.password?.message}
        disabled={isLoading}
        sx={{ mb: 3 }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Lock color="action" />
            </InputAdornment>
          ),
          endAdornment: (
            <InputAdornment position="end">
              <IconButton
                onClick={togglePasswordVisibility}
                edge="end"
                disabled={isLoading}
              >
                {showPassword ? <VisibilityOff /> : <Visibility />}
              </IconButton>
            </InputAdornment>
          ),
        }}
      />

      {/* Remember Me & Forgot Password */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 4,
        }}
      >
        <FormControlLabel
          control={
            <Switch
              {...register('rememberMe')}
              disabled={isLoading}
              color="primary"
            />
          }
          label={t('auth.rememberMe')}
        />

        <Button
          variant="text"
          size="small"
          disabled={isLoading}
          sx={{ textTransform: 'none' }}
        >
          {t('auth.forgotPassword')}
        </Button>
      </Box>

      {/* Login Button */}
      <Button
        type="submit"
        fullWidth
        variant="contained"
        size="large"
        disabled={isLoading}
        sx={{
          py: 1.5,
          fontSize: '1rem',
          fontWeight: 600,
          textTransform: 'none',
          borderRadius: 2,
          boxShadow: '0 4px 12px rgba(25, 118, 210, 0.3)',
          '&:hover': {
            boxShadow: '0 6px 16px rgba(25, 118, 210, 0.4)',
          },
        }}
      >
        {isLoading ? (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <CircularProgress size={20} color="inherit" />
            {t('auth.loggingIn')}
          </Box>
        ) : (
          t('auth.loginButton')
        )}
      </Button>

      {/* Additional Info */}
      <Box sx={{ mt: 4, textAlign: 'center' }}>
        <Typography variant="caption" color="text.secondary">
          Güvenli giriş için SSL şifreleme kullanılmaktadır
        </Typography>
      </Box>
    </Box>
  )
}
