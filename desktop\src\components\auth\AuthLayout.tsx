import React, { useState, useEffect } from 'react'
import { Box, Container, Paper, Typography, useTheme } from '@mui/material'
import { useTranslation } from 'react-i18next'
import { keyframes, css } from '@emotion/react'

// Interactive background animations
const float = keyframes`
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
`

const rotate = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`

const bounce = keyframes`
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-15px); }
`

const particleMove = keyframes`
  0% { transform: translateY(100vh) translateX(0); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(-100vh) translateX(100px); opacity: 0; }
`

const rippleAnimation = keyframes`
  0% {
    transform: scale(0);
    opacity: 0.6;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
`



interface AuthLayoutProps {
  children: React.ReactNode
}

interface RippleProps {
  x: number
  y: number
  id: string
}

interface ParticleProps {
  id: string
  left: number
  delay: number
  duration: number
}

export const AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {
  const theme = useTheme()
  const { t } = useTranslation()

  // Slider state
  const [currentSlide, setCurrentSlide] = useState(0)
  const [ripples, setRipples] = useState<RippleProps[]>([])
  const [particles, setParticles] = useState<ParticleProps[]>([])

  // Slider images - Local assets
  const sliderImages = [
    './assets/images/restaurant-1.jpg',
    './assets/images/restaurant-2.jpg',
    './assets/images/restaurant-3.jpg'
  ]

  // Auto slide effect
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % sliderImages.length)
    }, 4000)
    return () => clearInterval(interval)
  }, [sliderImages.length])

  // Create particles periodically
  useEffect(() => {
    const interval = setInterval(() => {
      const newParticle: ParticleProps = {
        id: Date.now().toString(),
        left: Math.random() * 100,
        delay: Math.random() * 10,
        duration: Math.random() * 10 + 10
      }
      setParticles(prev => [...prev, newParticle])
      
      // Remove particle after animation
      setTimeout(() => {
        setParticles(prev => prev.filter(p => p.id !== newParticle.id))
      }, 20000)
    }, 2000)
    
    return () => clearInterval(interval)
  }, [])

  // Handle click for ripple effect
  const handleBackgroundClick = (e: React.MouseEvent) => {
    if ((e.target as HTMLElement).closest('.floating-shape')) {
      return
    }
    
    const rect = e.currentTarget.getBoundingClientRect()
    const x = e.clientX - rect.left - 50
    const y = e.clientY - rect.top - 50
    
    const newRipple: RippleProps = {
      x,
      y,
      id: Date.now().toString()
    }
    
    setRipples(prev => [...prev, newRipple])
    
    setTimeout(() => {
      setRipples(prev => prev.filter(r => r.id !== newRipple.id))
    }, 1000)
  }

  // Handle floating shape click
  const handleShapeClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    
    const rect = e.currentTarget.getBoundingClientRect()
    const centerX = rect.left + rect.width / 2
    const centerY = rect.top + rect.height / 2
    
    // Create burst effect
    for (let i = 0; i < 8; i++) {
      setTimeout(() => {
        const angle = (i * 45) * Math.PI / 180
        const distance = 50
        const endX = Math.cos(angle) * distance
        const endY = Math.sin(angle) * distance
        
        // Create burst particle element
        const particle = document.createElement('div')
        particle.style.position = 'absolute'
        particle.style.width = '8px'
        particle.style.height = '8px'
        particle.style.background = '#2196F3'
        particle.style.borderRadius = '50%'
        particle.style.left = centerX + 'px'
        particle.style.top = centerY + 'px'
        particle.style.transform = 'translate(-50%, -50%)'
        particle.style.zIndex = '1000'
        particle.style.pointerEvents = 'none'
        particle.style.setProperty('--end-x', endX + 'px')
        particle.style.setProperty('--end-y', endY + 'px')
        particle.style.animation = 'particle-burst 0.8s ease-out forwards'
        
        document.body.appendChild(particle)
        
        setTimeout(() => particle.remove(), 800)
      }, i * 50)
    }
  }

  return (
    <Box
      sx={{
        height: '100vh',
        width: '100vw',
        display: 'flex',
        backgroundColor: '#FAFAFA',
        position: 'relative',
        overflow: 'hidden',
        margin: 0,
        padding: 0
      }}
      onClick={handleBackgroundClick}
    >
      {/* Interactive Background Container */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          overflow: 'hidden',
          zIndex: 1
        }}
      >
        {/* Floating Shapes */}
        <Box
          className="floating-shape"
          sx={{
            position: 'absolute',
            width: '80px',
            height: '80px',
            top: '20%',
            left: '10%',
            borderRadius: '50%',
            background: 'linear-gradient(45deg, #2196F3, #1976D2)',
            opacity: 0.2,
            animation: `${float} 6s ease-in-out infinite`,
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            '&:hover': {
              opacity: 0.4,
              transform: 'scale(1.2)'
            }
          }}
          onClick={handleShapeClick}
        />
        <Box
          className="floating-shape"
          sx={{
            position: 'absolute',
            width: '120px',
            height: '120px',
            top: '50%',
            right: '15%',
            borderRadius: '50%',
            background: 'linear-gradient(45deg, #42A5F5, #1E88E5)',
            opacity: 0.2,
            animation: `${float} 6s ease-in-out infinite 2s`,
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            '&:hover': {
              opacity: 0.4,
              transform: 'scale(1.2)'
            }
          }}
          onClick={handleShapeClick}
        />
        <Box
          className="floating-shape"
          sx={{
            position: 'absolute',
            width: '60px',
            height: '60px',
            bottom: '30%',
            left: '20%',
            borderRadius: '50%',
            background: 'linear-gradient(45deg, #64B5F6, #2196F3)',
            opacity: 0.2,
            animation: `${float} 6s ease-in-out infinite 4s`,
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            '&:hover': {
              opacity: 0.4,
              transform: 'scale(1.2)'
            }
          }}
          onClick={handleShapeClick}
        />
        <Box
          className="floating-shape"
          sx={{
            position: 'absolute',
            width: '100px',
            height: '100px',
            top: '10%',
            right: '30%',
            borderRadius: '50%',
            background: 'linear-gradient(45deg, #90CAF9, #42A5F5)',
            opacity: 0.2,
            animation: `${float} 6s ease-in-out infinite 1s`,
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            '&:hover': {
              opacity: 0.4,
              transform: 'scale(1.2)'
            }
          }}
          onClick={handleShapeClick}
        />
        <Box
          className="floating-shape"
          sx={{
            position: 'absolute',
            width: '90px',
            height: '90px',
            bottom: '20%',
            right: '40%',
            borderRadius: '50%',
            background: 'linear-gradient(45deg, #1976D2, #0D47A1)',
            opacity: 0.2,
            animation: `${float} 6s ease-in-out infinite 3s`,
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            '&:hover': {
              opacity: 0.4,
              transform: 'scale(1.2)'
            }
          }}
          onClick={handleShapeClick}
        />

        {/* Geometric Shapes */}
        <Box
          sx={{
            position: 'absolute',
            top: '15%',
            left: '25%',
            width: 0,
            height: 0,
            borderLeft: '15px solid transparent',
            borderRight: '15px solid transparent',
            borderBottom: '25px solid rgba(33, 150, 243, 0.15)',
            animation: `${rotate} 8s linear infinite`
          }}
        />
        <Box
          sx={{
            position: 'absolute',
            top: '60%',
            right: '20%',
            width: 0,
            height: 0,
            borderLeft: '15px solid transparent',
            borderRight: '15px solid transparent',
            borderBottom: '25px solid rgba(66, 165, 245, 0.15)',
            animation: `${rotate} 8s linear infinite 3s`
          }}
        />
        <Box
          sx={{
            position: 'absolute',
            bottom: '25%',
            left: '35%',
            width: 0,
            height: 0,
            borderLeft: '15px solid transparent',
            borderRight: '15px solid transparent',
            borderBottom: '25px solid rgba(25, 118, 210, 0.15)',
            animation: `${rotate} 8s linear infinite 6s`
          }}
        />
        <Box
          sx={{
            position: 'absolute',
            top: '30%',
            right: '10%',
            width: '20px',
            height: '20px',
            background: 'rgba(30, 136, 229, 0.15)',
            animation: `${bounce} 4s ease-in-out infinite 1s`
          }}
        />
        <Box
          sx={{
            position: 'absolute',
            bottom: '40%',
            left: '15%',
            width: '20px',
            height: '20px',
            background: 'rgba(100, 181, 246, 0.15)',
            animation: `${bounce} 4s ease-in-out infinite 2s`
          }}
        />

        {/* Particles */}
        {particles.map((particle) => (
          <Box
            key={particle.id}
            sx={{
              position: 'absolute',
              width: '4px',
              height: '4px',
              background: 'rgba(33, 150, 243, 0.4)',
              borderRadius: '50%',
              left: `${particle.left}vw`,
              animation: `${particleMove} ${particle.duration}s linear infinite ${particle.delay}s`
            }}
          />
        ))}

        {/* Ripples */}
        {ripples.map((ripple) => (
          <Box
            key={ripple.id}
            sx={{
              position: 'absolute',
              left: ripple.x,
              top: ripple.y,
              width: '100px',
              height: '100px',
              borderRadius: '50%',
              background: 'rgba(25, 118, 210, 0.4)',
              animation: `${rippleAnimation} 1s ease-out forwards`,
              pointerEvents: 'none'
            }}
          />
        ))}


      </Box>

      {/* Main Content */}
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%',
        height: '100%',
        p: { xs: 2, sm: 3, md: 4 },
        position: 'relative',
        zIndex: 2
      }}>
        <Paper
          elevation={8}
          sx={{
            display: 'flex',
            width: '100%',
            maxWidth: { xs: '100%', sm: 900, md: 1000 },
            height: { xs: '100%', sm: 'auto' },
            minHeight: { xs: '100%', sm: 600 },
            borderRadius: { xs: 0, sm: 3 },
            overflow: 'hidden',
            boxShadow: { xs: 'none', sm: '0 20px 40px rgba(0,0,0,0.1)' },
            flexDirection: { xs: 'column', md: 'row' }
          }}
        >
          {/* Left Side - Image Slider */}
          <Box
            sx={{
              flex: '0 0 60%',
              position: 'relative',
              minHeight: { xs: 300, sm: 400, md: 600 },
              overflow: 'hidden',
              order: { xs: 2, md: 1 },
              display: { xs: 'none', sm: 'block' }
            }}
          >
            {/* Image Slider */}
            {sliderImages.map((image, index) => (
              <Box
                key={index}
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundImage: `url(${image})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center right',
                  backgroundRepeat: 'no-repeat',
                  opacity: currentSlide === index ? 1 : 0,
                  transition: 'opacity 0.8s ease-in-out',
                }}
              />
            ))}

            {/* Slider Indicators */}
            <Box
              sx={{
                position: 'absolute',
                bottom: 30,
                left: '50%',
                transform: 'translateX(-50%)',
                display: 'flex',
                gap: 1.5,
                zIndex: 2,
                backgroundColor: 'rgba(0,0,0,0.3)',
                borderRadius: 3,
                p: 1.5,
                backdropFilter: 'blur(10px)'
              }}
            >
              {sliderImages.map((_, index) => (
                <Box
                  key={index}
                  sx={{
                    width: currentSlide === index ? 24 : 8,
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: currentSlide === index ? '#fff' : 'rgba(255,255,255,0.6)',
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    cursor: 'pointer',
                    '&:hover': {
                      backgroundColor: 'rgba(255,255,255,0.8)'
                    }
                  }}
                  onClick={() => setCurrentSlide(index)}
                />
              ))}
            </Box>

            {/* Testimonial */}
            <Box
              sx={{
                position: 'absolute',
                bottom: 30,
                left: 30,
                right: 30,
                zIndex: 3,
                p: 3,
                borderRadius: 2,
                backgroundColor: 'rgba(0,0,0,0.7)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255,255,255,0.2)',
                color: 'white',
              }}
            >
              <Typography
                variant="body1"
                sx={{
                  fontStyle: 'italic',
                  mb: 2,
                  lineHeight: 1.6,
                }}
              >
                "{t('auth.hero.testimonial')}"
              </Typography>
              <Box>
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                  {t('auth.hero.author')}
                </Typography>
                <Typography variant="caption" sx={{ opacity: 0.8 }}>
                  {t('auth.hero.position')}
                </Typography>
              </Box>
            </Box>
          </Box>

          {/* Right Side - Form Section */}
          <Box
            sx={{
              flex: '0 0 40%',
              p: { xs: 3, sm: 4, md: 6 },
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              backgroundColor: theme.palette.background.paper,
              minHeight: { xs: '100%', sm: 400, md: 600 },
              order: { xs: 1, md: 2 },
              width: { xs: '100%', md: 'auto' }
            }}
          >
            {children}
          </Box>
        </Paper>
      </Box>
    </Box>
  )
}