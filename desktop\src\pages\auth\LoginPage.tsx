import React, { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { AuthLayout } from '../../components/auth/AuthLayout'
import { LoginForm } from '../../components/auth/LoginForm'
import { useAuthStore } from '../../store/useAuthStore'

export const LoginPage: React.FC = () => {
  const navigate = useNavigate()
  const { isAuthenticated, refreshAuth } = useAuthStore()

  useEffect(() => {
    // Check if user is already authenticated
    refreshAuth()
  }, [refreshAuth])

  useEffect(() => {
    // Redirect if already authenticated
    if (isAuthenticated) {
      navigate('/dashboard', { replace: true })
    }
  }, [isAuthenticated, navigate])

  return (
    <AuthLayout>
      <LoginForm />
    </AuthLayout>
  )
}
